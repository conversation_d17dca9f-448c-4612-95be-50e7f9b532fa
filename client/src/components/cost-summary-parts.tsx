import { Category } from "shared"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

// Helper functions extracted from original CostSummary
const calculateCategoryTotal = (category: Category, priceType: 'selected' | 'cheapest' | 'most-expensive' = 'selected') => {
  return (category.items || []).reduce((total, item) => {
    if (!item.purchaseOptions || item.purchaseOptions.length === 0) return total

    const selectedOption = item.purchaseOptions.find(opt => opt.id === item.selectedOptionId)
    const cheapestOption = item.purchaseOptions.reduce((prev, current) =>
      (prev.price || 0) < (current.price || 0) ? prev : current, item.purchaseOptions[0]
    )
    const expensiveOption = item.purchaseOptions.reduce((prev, current) =>
      (prev.price || 0) > (current.price || 0) ? prev : current, item.purchaseOptions[0]
    )

    switch (priceType) {
      case 'cheapest':
        return total + (cheapestOption.price || 0)
      case 'most-expensive':
        return total + (expensiveOption.price || 0)
      default:
        return total + (selectedOption?.price || cheapestOption.price || 0)
    }
  }, 0)
}

const calculateCategoryMinMax = (category: Category) => {
  let min = 0, max = 0

    (category.items || []).forEach(item => {
      if (item.purchaseOptions && item.purchaseOptions.length > 0) {
        const prices = item.purchaseOptions.map(opt => opt.price || 0)
        if (prices.length > 0) {
          min += Math.min(...prices)
          max += Math.max(...prices)
        }
      }
    })

  return { min, max }
}

export function CostSummaryFirst({
  categories = [],
  currentCategoryId,
  budget,
}: {
  categories?: Category[]
  currentCategoryId?: string
  budget?: number
}) {
  const currentCategory = categories.find(cat => cat.id === currentCategoryId)

  const bucketTotal = categories.reduce((total, category) => total + calculateCategoryTotal(category), 0)
  const bucketMinTotal = categories.reduce((total, category) => total + calculateCategoryTotal(category, 'cheapest'), 0)
  const bucketMaxTotal = categories.reduce((total, category) => total + calculateCategoryTotal(category, 'most-expensive'), 0)

  const potentialSavings = bucketTotal - bucketMinTotal
  const budgetUsed = budget ? (bucketTotal / budget) * 100 : 0
  const budgetStatus = budget ? (
    budgetUsed <= 50 ? 'safe' :
      budgetUsed <= 80 ? 'warning' :
        'danger'
  ) : null

  return (
    <div className="space-y-4">
      {budget && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Budget Tracking</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Budget Used</span>
                <span>{Math.round(budgetUsed)}%</span>
              </div>
              <Progress
                value={Math.min(budgetUsed, 100)}
                className={`h-2 ${budgetStatus === 'danger' ? '[&>div]:bg-red-500' :
                    budgetStatus === 'warning' ? '[&>div]:bg-yellow-500' :
                      '[&>div]:bg-green-500'
                  }`}
              />
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Current Total</span>
              <span className="font-semibold">${bucketTotal.toFixed(2)}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Budget</span>
              <span className="font-medium">${budget.toFixed(2)}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Remaining</span>
              <span className={`font-medium ${budget - bucketTotal < 0 ? 'text-red-600' : 'text-green-600'
                }`}>
                ${(budget - bucketTotal).toFixed(2)}
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {currentCategory && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Current Category</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">{currentCategory.name}</span>
              <span className="font-semibold">${calculateCategoryTotal(currentCategory).toFixed(2)}</span>
            </div>

            {(currentCategory.items || []).length > 0 && (
              <div className="text-xs text-muted-foreground">
                {(() => {
                  const { min, max } = calculateCategoryMinMax(currentCategory)
                  return min !== max ? `Range: $${min.toFixed(2)} - $${max.toFixed(2)}` : ''
                })()}
              </div>
            )}

            <div className="text-xs text-muted-foreground">
              {(currentCategory.items || []).length} {(currentCategory.items || []).length === 1 ? 'item' : 'items'}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Price Analysis</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center">
              <div className="text-sm font-semibold text-green-600">${bucketMinTotal.toFixed(2)}</div>
              <div className="text-xs text-muted-foreground">Best Case</div>
            </div>
            <div className="text-center">
              <div className="text-sm font-semibold text-red-600">${bucketMaxTotal.toFixed(2)}</div>
              <div className="text-xs text-muted-foreground">Worst Case</div>
            </div>
          </div>

          {potentialSavings > 0 && (
            <>
              <Separator />
              <div className="text-center">
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  Save up to ${potentialSavings.toFixed(2)}
                </Badge>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export function CostSummaryLast({
  categories = [],
}: {
  categories?: Category[]
}) {
  const calculateCategoryTotalLast = (category: Category) => {
    return (category.items || []).reduce((total, item) => {
      if (!item.purchaseOptions || item.purchaseOptions.length === 0) return total

      const selectedOption = item.purchaseOptions.find(opt => opt.id === item.selectedOptionId)
      const cheapestOption = item.purchaseOptions.reduce((prev, current) =>
        (prev.price || 0) < (current.price || 0) ? prev : current, item.purchaseOptions[0]
      )

      return total + (selectedOption?.price || cheapestOption.price || 0)
    }, 0)
  }

  const bucketTotal = categories.reduce((total, category) => total + calculateCategoryTotalLast(category), 0)

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Bucket Overview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {categories.map((category) => {
            const total = calculateCategoryTotalLast(category)
            if (total === 0 && (category.items || []).length === 0) return null

            return (
              <div key={category.id} className="flex justify-between items-center text-sm">
                <span className="text-muted-foreground">{category.name}</span>
                <span className="font-medium">${total.toFixed(2)}</span>
              </div>
            )
          })}

          {categories.length > 0 && (
            <>
              <Separator />
              <div className="flex justify-between items-center font-semibold">
                <span>Total</span>
                <span>${bucketTotal.toFixed(2)}</span>
              </div>
            </>
          )}

          {categories.length === 0 && (
            <div className="text-center text-muted-foreground text-sm py-4">
              No categories yet
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Quick Stats</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Categories</span>
            <span>{categories.length}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Total Items</span>
            <span>{categories.reduce((total, cat) => total + (cat.items || []).length, 0)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">With Prices</span>
            <span>
              {categories.reduce((total, cat) =>
                total + (cat.items || []).filter(item => (item.purchaseOptions || []).length > 0).length, 0
              )}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}